import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'
import { generateUniversalId, getIdFieldConfig } from '../../hooks/generateUniversalId'
import { directImageUploadField, enhancedImageField } from '../../fields/imageUpload'

export const News: CollectionConfig = {
  slug: 'news',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'status', 'publishDate', 'author'],
    group: 'Content Management',
  },
  labels: {
    singular: 'News Article',
    plural: 'News & Updates',
  },
  fields: [
    getIdFieldConfig('news'),
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The news article title',
      },
    },
    {
      name: 'subtitle',
      type: 'text',
      admin: {
        description: 'Optional subtitle or tagline',
      },
    },
    {
      name: 'summary',
      type: 'textarea',
      required: true,
      maxLength: 300,
      admin: {
        description: 'Brief article summary for cards and previews (max 300 characters)',
      },
    },
    {
      name: 'content',
      type: 'richText',
      required: true,
      admin: {
        description: 'Full article content with rich text formatting',
      },
    },
    enhancedImageField({
      name: 'featuredImage',
      label: 'Featured Image',
      required: false, // Made optional to prevent creation errors when media collection is empty
      admin: {
        description: 'Main article image with alt text and caption (optional - can be added later)',
      },
    }),
    {
      name: 'gallery',
      type: 'array',
      fields: [
        directImageUploadField({
          name: 'image',
          label: 'Gallery Image',
          required: true,
          admin: {
            description: 'Upload a gallery image',
          },
        }),
        {
          name: 'caption',
          type: 'text',
        },
        {
          name: 'credit',
          type: 'text',
        },
      ],
      admin: {
        description: 'Additional article images',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'News', value: 'news' },
        { label: 'Press Release', value: 'press-release' },
        { label: 'Blog Post', value: 'blog-post' },
        { label: 'Event Update', value: 'event-update' },
        { label: 'Project Update', value: 'project-update' },
        { label: 'Policy Update', value: 'policy-update' },
        { label: 'Partnership Announcement', value: 'partnership-announcement' },
        { label: 'Success Story', value: 'success-story' },
        { label: 'Research Update', value: 'research-update' },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'draft',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Review', value: 'review' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
    },
    {
      name: 'publishDate',
      type: 'date',
      required: true,
      admin: {
        description: 'When this article should be published',
      },
    },
    {
      name: 'author',
      type: 'group',
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'role',
          type: 'text',
        },
        {
          name: 'organization',
          type: 'text',
        },
        {
          name: 'bio',
          type: 'textarea',
        },
        {
          name: 'photo',
          type: 'upload',
          relationTo: 'media',
        },
        {
          name: 'email',
          type: 'email',
        },
        {
          name: 'socialLinks',
          type: 'array',
          fields: [
            {
              name: 'platform',
              type: 'select',
              options: [
                { label: 'Twitter', value: 'twitter' },
                { label: 'LinkedIn', value: 'linkedin' },
                { label: 'Facebook', value: 'facebook' },
                { label: 'Website', value: 'website' },
              ],
            },
            {
              name: 'url',
              type: 'text',
              required: true,
            },
          ],
        },
      ],
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'county',
          type: 'relationship',
          relationTo: 'counties',
        },
        {
          name: 'specificLocation',
          type: 'text',
        },
        {
          name: 'coordinates',
          type: 'group',
          fields: [
            {
              name: 'latitude',
              type: 'number',
            },
            {
              name: 'longitude',
              type: 'number',
            },
          ],
        },
      ],
    },
    {
      name: 'relatedContent',
      type: 'group',
      fields: [
        {
          name: 'projects',
          type: 'relationship',
          relationTo: 'projects',
          hasMany: true,
        },
        {
          name: 'successStories',
          type: 'relationship',
          relationTo: 'success-stories',
          hasMany: true,
        },
        {
          name: 'events',
          type: 'relationship',
          relationTo: 'events',
          hasMany: true,
        },
        {
          name: 'resources',
          type: 'relationship',
          relationTo: 'resources',
          hasMany: true,
        },
      ],
    },
    {
      name: 'seo',
      type: 'group',
      fields: [
        {
          name: 'metaTitle',
          type: 'text',
          maxLength: 60,
        },
        {
          name: 'metaDescription',
          type: 'textarea',
          maxLength: 160,
        },
        {
          name: 'keywords',
          type: 'array',
          fields: [
            {
              name: 'keyword',
              type: 'text',
            },
          ],
        },
        {
          name: 'ogImage',
          type: 'upload',
          relationTo: 'media',
        },
      ],
    },
    {
      name: 'engagement',
      type: 'group',
      fields: [
        {
          name: 'allowComments',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'socialSharing',
          type: 'checkbox',
          defaultValue: true,
        },
        {
          name: 'newsletter',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Include in newsletter',
          },
        },
      ],
    },
    {
      name: 'analytics',
      type: 'group',
      fields: [
        {
          name: 'viewCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'shareCount',
          type: 'number',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'lastViewed',
          type: 'date',
          admin: {
            readOnly: true,
          },
        },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this article on homepage and key sections',
      },
    },
    {
      name: 'urgent',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Mark as urgent/breaking news',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for search and filtering',
      },
    },
    ...slugField(),
  ],
  hooks: {
    beforeChange: [generateUniversalId],
  },
}

export default News
