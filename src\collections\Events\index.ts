import { CollectionConfig } from "payload";
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'
import { generateUniversalId, getIdFieldConfig } from '../../hooks/generateUniversalId';
import { directImageUploadField, enhancedImageField } from '../../fields/imageUpload';
import { sanitizeForDatabase } from '../../hooks/sanitizeForDatabase';

const Events: CollectionConfig = {
  slug: 'events',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'date', 'type', 'location', 'status'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Event',
    plural: 'Events',
  },
  fields: [
    getIdFieldConfig('events'),
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
    },
    {
      name: 'summary',
      type: 'textarea',
      required: true,
      maxLength: 300,
      admin: {
        description: 'Brief event summary for cards and previews (max 300 characters)',
      },
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Conference', value: 'conference' },
        { label: 'Workshop', value: 'workshop' },
        { label: 'Seminar', value: 'seminar' },
        { label: 'Training', value: 'training' },
        { label: 'Community Meeting', value: 'community-meeting' },
        { label: 'Exhibition', value: 'exhibition' },
        { label: 'Networking', value: 'networking' },
        { label: 'Launch Event', value: 'launch' },
      ],
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Knowledge Sharing', value: 'knowledge-sharing' },
        { label: 'Capacity Building', value: 'capacity-building' },
        { label: 'Community Engagement', value: 'community-engagement' },
        { label: 'Policy & Advocacy', value: 'policy-advocacy' },
        { label: 'Research & Development', value: 'research-development' },
        { label: 'Market Development', value: 'market-development' },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'upcoming',
      options: [
        { label: 'Upcoming', value: 'upcoming' },
        { label: 'Ongoing', value: 'ongoing' },
        { label: 'Completed', value: 'completed' },
        { label: 'Cancelled', value: 'cancelled' },
        { label: 'Postponed', value: 'postponed' },
      ],
    },
    {
      name: 'schedule',
      type: 'group',
      fields: [
        {
          name: 'startDate',
          type: 'date',
          required: true,
        },
        {
          name: 'endDate',
          type: 'date',
        },
        {
          name: 'startTime',
          type: 'text',
          admin: {
            description: 'Start time (e.g., "09:00 AM")',
          },
        },
        {
          name: 'endTime',
          type: 'text',
          admin: {
            description: 'End time (e.g., "05:00 PM")',
          },
        },
        {
          name: 'timezone',
          type: 'select',
          defaultValue: 'EAT',
          options: [
            { label: 'East Africa Time (EAT)', value: 'EAT' },
            { label: 'UTC', value: 'UTC' },
          ],
        },
      ],
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'venue',
          type: 'text',
          required: true,
          admin: {
            description: 'Venue name or location',
          },
        },
        {
          name: 'address',
          type: 'textarea',
          admin: {
            description: 'Full address',
          },
        },
        {
          name: 'county',
          type: 'relationship',
          relationTo: 'counties',
        },
        {
          name: 'coordinates',
          type: 'group',
          fields: [
            {
              name: 'latitude',
              type: 'number',
            },
            {
              name: 'longitude',
              type: 'number',
            },
          ],
        },
        {
          name: 'isVirtual',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'virtualLink',
          type: 'text',
          admin: {
            condition: (data, siblingData) => siblingData?.isVirtual,
            description: 'Virtual meeting link (if applicable)',
          },
        },
      ],
    },
    enhancedImageField({
      name: 'image',
      label: 'Event Image',
      required: false,
      admin: {
        description: 'Main event image for promotional materials',
      },
    }),
    {
      name: 'speakers',
      type: 'relationship',
      relationTo: 'speakers',
      hasMany: true,
    },
    {
      name: 'organizers',
      type: 'array',
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'organization',
          type: 'text',
        },
        {
          name: 'role',
          type: 'text',
        },
        {
          name: 'contact',
          type: 'group',
          fields: [
            {
              name: 'email',
              type: 'email',
            },
            {
              name: 'phone',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'registration',
      type: 'group',
      fields: [
        {
          name: 'required',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'deadline',
          type: 'date',
          admin: {
            condition: (data, siblingData) => siblingData?.required,
          },
        },
        {
          name: 'link',
          type: 'text',
          admin: {
            condition: (data, siblingData) => siblingData?.required,
            description: 'Registration link or form URL',
          },
        },
        {
          name: 'capacity',
          type: 'number',
          admin: {
            description: 'Maximum number of participants',
          },
        },
        {
          name: 'fee',
          type: 'group',
          fields: [
            {
              name: 'amount',
              type: 'number',
            },
            {
              name: 'currency',
              type: 'select',
              defaultValue: 'KES',
              options: [
                { label: 'KES', value: 'KES' },
                { label: 'USD', value: 'USD' },
                { label: 'Free', value: 'FREE' },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'resources',
      type: 'group',
      fields: [
        {
          name: 'documents',
          type: 'array',
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
            },
            {
              name: 'file',
              type: 'upload',
              relationTo: 'media',
              required: false, // Made optional to prevent creation errors when media collection is empty
              admin: {
                description: 'Upload document (optional - can be added later)',
                allowCreate: true, // Allow direct upload without requiring existing media
              },
            },
            {
              name: 'type',
              type: 'select',
              options: [
                { label: 'Agenda', value: 'agenda' },
                { label: 'Presentation', value: 'presentation' },
                { label: 'Report', value: 'report' },
                { label: 'Brochure', value: 'brochure' },
                { label: 'Other', value: 'other' },
              ],
            },
          ],
        },
        {
          name: 'links',
          type: 'array',
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
            },
            {
              name: 'url',
              type: 'text',
              required: true,
            },
            {
              name: 'description',
              type: 'textarea',
            },
          ],
        },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this event on homepage and key sections',
      },
    },
    {
      name: 'published',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Make this event visible to the public',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for search and filtering',
      },
    },
    ...slugField(),
  ],
  hooks: {
    beforeChange: [generateUniversalId, sanitizeForDatabase],
  },
}

export default Events
