'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import { X, ExternalLink, MapPin, Calendar, Users } from 'lucide-react'

interface Partner {
  id: string
  name: string
  logo: string
  category: string
  location: string
  established: string
  description: string
  fullBio: string
  website?: string
  keyAchievements: string[]
  collaborationAreas: string[]
  partnershipSince: string
}

interface NPIPartnersShowcaseProps {
  title?: string
  description?: string
  partners?: Partner[]
}

export const NPIPartnersShowcaseBlock: React.FC<NPIPartnersShowcaseProps> = ({
  title = 'Partner Network',
  description = "Discover the diverse organizations and institutions that collaborate with us to drive sustainable development in Kenya's natural products sector.",
}) => {
  const [partners, setPartners] = useState<Partner[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPartners = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch('/api/partners?limit=6&status=active')

        if (!response.ok) {
          throw new Error(`Failed to fetch partners: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.partners) {
          // Transform database data to match component interface
          const transformedPartners = data.partners.map((partner: any) => {
            // Safely extract location information
            const address = partner.contact?.address
            let locationString = 'Kenya'

            if (address) {
              const parts = []
              if (address.city) parts.push(address.city)
              if (address.county && typeof address.county === 'object' && address.county.name) {
                parts.push(address.county.name)
              } else if (address.county && typeof address.county === 'string') {
                parts.push(address.county)
              }
              if (address.country) parts.push(address.country)

              if (parts.length > 0) {
                locationString = parts.join(', ')
              }
            }

            return {
              id: partner.id,
              name: partner.name,
              logo: partner.logo?.url || 'https://picsum.photos/200/200?random=1',
              category: partner.category || partner.type || 'Partner',
              location: locationString,
              established: partner.established || '2020',
              description: partner.summary || partner.description || 'Strategic partner in natural products development.',
              fullBio: partner.description || 'Detailed information about this partner.',
              website: partner.contact?.website,
              keyAchievements: partner.achievements || [
                'Strategic partnership development',
                'Community impact initiatives',
                'Sustainable development projects'
              ],
              collaborationAreas: partner.collaborationAreas || [
                'Natural products development',
                'Community empowerment',
                'Sustainable practices'
              ],
              partnershipSince: partner.partnershipSince || '2020',
            }
          })
          setPartners(transformedPartners)
        } else {
          throw new Error('Invalid response format')
        }
      } catch (err) {
        console.error('Error fetching partners:', err)
        setError(err instanceof Error ? err.message : 'Failed to load partners')
        // Fallback to empty array
        setPartners([])
      } finally {
        setLoading(false)
      }
    }

    fetchPartners()
  }, [])






  // Use only database data - no fallback mock data
  const displayPartners = partners
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null)

  const getCardDesign = (index: number) => {
    const designs = [
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#725242]',
        text: 'text-black',
        logoContainer: 'bg-[#725242]/15 border-[#725242]/40',
        categoryBg: 'bg-[#725242] text-white',
        buttonHover: 'hover:bg-[#725242]/15',
        iconColor: 'text-[#725242]',
        divider: 'border-[#725242]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#8A3E25]',
        text: 'text-black',
        logoContainer: 'bg-[#8A3E25]/15 border-[#8A3E25]/40',
        categoryBg: 'bg-[#8A3E25] text-white',
        buttonHover: 'hover:bg-[#8A3E25]/15',
        iconColor: 'text-[#8A3E25]',
        divider: 'border-[#8A3E25]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#25718A]',
        text: 'text-black',
        logoContainer: 'bg-[#25718A]/15 border-[#25718A]/40',
        categoryBg: 'bg-[#25718A] text-white',
        buttonHover: 'hover:bg-[#25718A]/15',
        iconColor: 'text-[#25718A]',
        divider: 'border-[#25718A]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#725242]',
        text: 'text-black',
        logoContainer: 'bg-white border-[#725242]/40',
        categoryBg: 'bg-[#725242] text-white',
        buttonHover: 'hover:bg-[#725242]/15',
        iconColor: 'text-[#725242]',
        divider: 'border-[#725242]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#8A3E25]',
        text: 'text-black',
        logoContainer: 'bg-white border-[#8A3E25]/40',
        categoryBg: 'bg-[#8A3E25] text-white',
        buttonHover: 'hover:bg-[#8A3E25]/15',
        iconColor: 'text-[#8A3E25]',
        divider: 'border-[#8A3E25]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#25718A]',
        text: 'text-black',
        logoContainer: 'bg-white border-[#25718A]/40',
        categoryBg: 'bg-[#25718A] text-white',
        buttonHover: 'hover:bg-[#25718A]/15',
        iconColor: 'text-[#25718A]',
        divider: 'border-[#25718A]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#725242]',
        text: 'text-black',
        logoContainer: 'bg-[#725242]/15 border-[#725242]/40',
        categoryBg: 'bg-[#725242] text-white',
        buttonHover: 'hover:bg-[#725242]/15',
        iconColor: 'text-[#725242]',
        divider: 'border-[#725242]/30',
      },
      {
        bg: 'bg-[#EFE3BA]',
        border: 'border-[#8A3E25]',
        text: 'text-black',
        logoContainer: 'bg-[#8A3E25]/15 border-[#8A3E25]/40',
        categoryBg: 'bg-[#8A3E25] text-white',
        buttonHover: 'hover:bg-[#8A3E25]/15',
        iconColor: 'text-[#8A3E25]',
        divider: 'border-[#8A3E25]/30',
      },
    ]
    return designs[index % designs.length]
  }

  return (
    <NPISection className="bg-white py-16">
      <NPISectionHeader className="mb-12">
        <NPISectionTitle className="text-[#725242] text-4xl font-bold mb-6 font-npi">
          {title}
        </NPISectionTitle>
        <NPISectionDescription className="text-black text-lg font-medium max-w-4xl mx-auto leading-relaxed">
          {description}
        </NPISectionDescription>
      </NPISectionHeader>

      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-black">Loading partners...</div>
        </div>
      )}

      {error && (
        <div className="flex justify-center items-center py-12">
          <div className="text-red-600">Error: {error}</div>
        </div>
      )}

      {!loading && !error && (
        <>
          {/* Partners Grid - 3 cards per row for better size */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {displayPartners.map((partner, index) => {
          const design = getCardDesign(index)
          return (
            <motion.div
              key={partner.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{
                y: -12,
                scale: 1.03,
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                transition: { duration: 0.3, ease: 'easeOut' },
              }}
              className="group"
            >
              <NPICard
                className={`cursor-pointer aspect-square flex flex-col ${design.bg} ${design.border} border-2 transition-all duration-300 overflow-hidden group-hover:border-opacity-80 shadow-lg group-hover:shadow-2xl hover:scale-105`}
                onClick={() => setSelectedPartner(partner)}
              >
                {/* Square card header - minimal */}
                <NPICardHeader className="flex-shrink-0 p-3">
                  <div className="flex flex-col items-center text-center">
                    <div
                      className={`w-16 h-16 ${design.logoContainer} border-2 flex items-center justify-center mb-2 overflow-hidden transition-all duration-300 group-hover:scale-110`}
                    >
                      <Image
                        src={partner.logo}
                        alt={`${partner.name} logo`}
                        width={64}
                        height={64}
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                    <NPICardTitle
                      className={`text-sm mb-2 leading-tight ${design.text} font-npi font-bold text-center line-clamp-2`}
                    >
                      {partner.name}
                    </NPICardTitle>
                    <span
                      className={`px-2 py-1 text-xs font-semibold ${design.categoryBg} transition-all duration-300`}
                    >
                      {partner.category}
                    </span>
                  </div>
                </NPICardHeader>

                {/* Minimal content for square cards */}
                <NPICardContent className="p-3 pt-0 flex-1 flex flex-col justify-between">
                  <div className="space-y-2">
                    {/* Essential info only */}
                    <div className="text-xs text-[#725242]/70 font-medium">
                      {partner.location}
                    </div>
                    <div className="text-xs text-[#725242]/70 font-medium">
                      Since {partner.partnershipSince}
                    </div>
                    <p className={`text-xs leading-tight ${design.text} font-medium line-clamp-3`}>
                      {partner.description}
                    </p>
                  </div>

                  {/* Dynamic action buttons */}
                  <div className="mt-3 space-y-2">
                    <NPIButton
                      size="sm"
                      className="w-full bg-[#8A3E25] hover:bg-[#8A3E25]/90 text-white font-semibold py-2 text-xs"
                      onClick={(e) => {
                        e.stopPropagation()
                        setSelectedPartner(partner)
                      }}
                    >
                      View Details
                    </NPIButton>
                    <NPIButton
                      size="sm"
                      variant="outline"
                      className="w-full border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white font-semibold py-2 text-xs"
                      onClick={(e) => {
                        e.stopPropagation()
                        if (partner.website) {
                          window.open(partner.website, '_blank')
                        }
                      }}
                    >
                      Contact Partner
                    </NPIButton>
                  </div>
                </NPICardContent>
              </NPICard>
            </motion.div>
          )
        })}
      </div>

      {/* Modal */}
      <AnimatePresence>
        {selectedPartner && (
          <motion.div
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedPartner(null)}
          >
            <motion.div
              className="bg-white max-w-2xl w-full max-h-[90vh] overflow-y-auto border-2 border-[#725242]"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="bg-[#8A3E25] p-6 text-white">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-white/20 border border-white/30 flex items-center justify-center overflow-hidden">
                      <Image
                        src={selectedPartner.logo}
                        alt={`${selectedPartner.name} logo`}
                        width={64}
                        height={64}
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold mb-1 font-npi">{selectedPartner.name}</h3>
                      <span className="px-3 py-1 bg-white/20 text-sm font-medium">
                        {selectedPartner.category}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedPartner(null)}
                    className="p-2 hover:bg-white/20 transition-colors"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3 text-black font-npi">Partner Information</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-[#8A3E25]" />
                        <span className="text-black">{selectedPartner.location}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-[#8A3E25]" />
                        <span className="text-black">
                          Established {selectedPartner.established}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-[#8A3E25]" />
                        <span className="text-black">
                          Partner since {selectedPartner.partnershipSince}
                        </span>
                      </div>
                      {selectedPartner.website && (
                        <div className="flex items-center gap-2">
                          <ExternalLink className="w-4 h-4 text-[#25718A]" />
                          <a
                            href={selectedPartner.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-[#25718A] hover:underline transition-colors duration-300"
                          >
                            Visit Website
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-3 text-black font-npi">Collaboration Areas</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedPartner.collaborationAreas.map((area, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-[#EFE3BA] text-[#725242] text-xs border border-[#725242]/20"
                        >
                          {area}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-black font-npi">About</h4>
                  <p className="text-black leading-relaxed">{selectedPartner.fullBio}</p>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-black font-npi">Key Achievements</h4>
                  <ul className="space-y-2">
                    {selectedPartner.keyAchievements.map((achievement, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-[#8A3E25] mt-2 flex-shrink-0"></div>
                        <span className="text-black text-sm">{achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
        </>
      )}
    </NPISection>
  )
}
