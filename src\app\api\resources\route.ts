import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { resourcesHand<PERSON> } from '@/endpoints/resources'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      query: Object.fromEntries(request.nextUrl.searchParams.entries()),
      headers: {
        get: (name: string) => request.headers.get(name),
      },
      user: null,
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the handler
    await resourcesHandler(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('Resources API error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Create new resource
    const result = await payload.create({
      collection: 'resources',
      data: body,
    })

    return NextResponse.json({
      success: true,
      resource: result,
      message: 'Resource created successfully',
    })
  } catch (error) {
    console.error('Resources POST API error:', error)

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          message: error.message,
          details: error.data || error.details || null,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
