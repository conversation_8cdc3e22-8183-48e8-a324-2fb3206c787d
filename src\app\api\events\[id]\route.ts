import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

interface RouteParams {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    let result

    // First try to find by ID (if it's a valid ObjectId format)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      try {
        result = await payload.findByID({
          collection: 'events',
          id,
        })
      } catch (error) {
        // If ID lookup fails, try slug lookup
        result = null
      }
    }

    // If no result from ID lookup or ID is not ObjectId format, try slug lookup
    if (!result) {
      const slugResult = await payload.find({
        collection: 'events',
        where: {
          slug: {
            equals: id,
          },
        },
        limit: 1,
      })

      if (slugResult.docs.length > 0) {
        result = slugResult.docs[0]
      }
    }

    if (!result) {
      return NextResponse.json(
        {
          success: false,
          error: 'Event not found',
          message: `Event with ID or slug '${id}' not found`,
        },
        { status: 404 },
      )
    }

    return NextResponse.json({
      success: true,
      event: result,
    })
  } catch (error) {
    console.error('Event GET API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    // First find the event to get its actual ID
    let eventId = id

    // If not a valid ObjectId, try to find by slug
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      const slugResult = await payload.find({
        collection: 'events',
        where: {
          slug: {
            equals: id,
          },
        },
        limit: 1,
      })

      if (slugResult.docs.length === 0) {
        return NextResponse.json(
          {
            error: 'Event not found',
            message: `Event with slug '${id}' not found`,
          },
          { status: 404 },
        )
      }

      eventId = slugResult.docs[0].id
    }

    const body = await request.json()

    // Update the event
    const result = await payload.update({
      collection: 'events',
      id: eventId,
      data: body,
    })

    return NextResponse.json({
      success: true,
      event: result,
      message: 'Event updated successfully',
    })
  } catch (error) {
    console.error('Event PUT API error:', error)

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          message: error.message,
          details: error.data || error.details || null,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    // First find the event to get its actual ID
    let eventId = id

    // If not a valid ObjectId, try to find by slug
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      const slugResult = await payload.find({
        collection: 'events',
        where: {
          slug: {
            equals: id,
          },
        },
        limit: 1,
      })

      if (slugResult.docs.length === 0) {
        return NextResponse.json(
          {
            error: 'Event not found',
            message: `Event with slug '${id}' not found`,
          },
          { status: 404 },
        )
      }

      eventId = slugResult.docs[0].id
    }

    // Delete the event
    await payload.delete({
      collection: 'events',
      id: eventId,
    })

    return NextResponse.json({
      success: true,
      message: 'Event deleted successfully',
    })
  } catch (error) {
    console.error('Event DELETE API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}
