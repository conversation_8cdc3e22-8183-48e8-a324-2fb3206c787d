import type {
  CMSProject,
  CMSSuccessStory,
  CMSResource,
  CMSNews,
  CMSMediaItem,
  CMSInvestmentOpportunity,
  CMSQueryParams,
  CMSListResponse,
  CMSResponse,
} from './types'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ''

class CMSApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
  ) {
    super(message)
    this.name = 'CMSApiError'
  }
}

async function fetchAPI<T>(
  endpoint: string,
  options: RequestInit = {},
): Promise<T> {
  const url = `${API_BASE_URL}/api${endpoint}`
  
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new CMSApiError(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorData.code,
      )
    }

    return await response.json()
  } catch (error) {
    if (error instanceof CMSApiError) {
      throw error
    }
    throw new CMSApiError(
      error instanceof Error ? error.message : 'Unknown error occurred',
    )
  }
}

function buildQueryString(params: CMSQueryParams): string {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value))
    }
  })
  
  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}

// Projects API
export const projectsAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSProject>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{
      success: boolean;
      projects: CMSProject[];
      pagination: {
        totalDocs: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
      }
    }>(`/projects${queryString}`)

    return {
      data: response.projects,
      totalDocs: response.pagination.totalDocs,
      page: response.pagination.page,
      limit: response.pagination.limit,
      totalPages: response.pagination.totalPages,
      hasNextPage: response.pagination.hasNextPage,
      hasPrevPage: response.pagination.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSProject>> {
    const response = await fetchAPI<{ project: CMSProject }>(`/projects/${id}`)
    return { data: response.project }
  },

  async getFeatured(limit = 6): Promise<CMSListResponse<CMSProject>> {
    return this.getAll({ featured: true, limit })
  },

  async getByCategory(category: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSProject>> {
    return this.getAll({ ...params, category })
  },
}

// Success Stories API
export const successStoriesAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSSuccessStory>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{
      success: boolean;
      stories: CMSSuccessStory[];
      pagination: {
        totalDocs: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
      }
    }>(`/success-stories${queryString}`)

    return {
      data: response.stories,
      totalDocs: response.pagination.totalDocs,
      page: response.pagination.page,
      limit: response.pagination.limit,
      totalPages: response.pagination.totalPages,
      hasNextPage: response.pagination.hasNextPage,
      hasPrevPage: response.pagination.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSSuccessStory>> {
    const response = await fetchAPI<{ story: CMSSuccessStory }>(`/success-stories/${id}`)
    return { data: response.story }
  },

  async getFeatured(limit = 6): Promise<CMSListResponse<CMSSuccessStory>> {
    return this.getAll({ featured: true, limit })
  },

  async getByCategory(category: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSSuccessStory>> {
    return this.getAll({ ...params, category })
  },
}

// Resources API
export const resourcesAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSResource>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{ resources: CMSResource[]; totalResources: number; page: number; limit: number; totalPages: number; hasNextPage: boolean; hasPrevPage: boolean }>(`/resources${queryString}`)
    
    return {
      data: response.resources,
      totalDocs: response.totalResources,
      page: response.page,
      limit: response.limit,
      totalPages: response.totalPages,
      hasNextPage: response.hasNextPage,
      hasPrevPage: response.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSResource>> {
    const response = await fetchAPI<{ resource: CMSResource }>(`/resources/${id}`)
    return { data: response.resource }
  },

  async getFeatured(limit = 6): Promise<CMSListResponse<CMSResource>> {
    return this.getAll({ featured: true, limit })
  },

  async getByType(type: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSResource>> {
    return this.getAll({ ...params, type })
  },

  async getByCategory(category: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSResource>> {
    return this.getAll({ ...params, category })
  },

  async download(id: string): Promise<{ downloadUrl: string; filename?: string; filesize?: number; mimeType?: string }> {
    return await fetchAPI<{ downloadUrl: string; filename?: string; filesize?: number; mimeType?: string }>(`/resources/${id}/download`)
  },
}

// News API
export const newsAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSNews>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{
      success: boolean;
      news: CMSNews[];
      pagination: {
        totalDocs: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
      }
    }>(`/news${queryString}`)

    return {
      data: response.news,
      totalDocs: response.pagination.totalDocs,
      page: response.pagination.page,
      limit: response.pagination.limit,
      totalPages: response.pagination.totalPages,
      hasNextPage: response.pagination.hasNextPage,
      hasPrevPage: response.pagination.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSNews>> {
    const response = await fetchAPI<{ article: CMSNews }>(`/news/${id}`)
    return { data: response.article }
  },

  async getFeatured(limit = 6): Promise<CMSListResponse<CMSNews>> {
    return this.getAll({ featured: true, limit })
  },

  async getUrgent(limit = 3): Promise<CMSListResponse<CMSNews>> {
    return this.getAll({ urgent: true, limit })
  },

  async getByCategory(category: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSNews>> {
    return this.getAll({ ...params, category })
  },

  async create(data: Partial<CMSNews>): Promise<CMSNews> {
    const response = await fetchAPI<{
      success: boolean;
      news: CMSNews;
    }>('/news', {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.news
  },

  async update(id: string, data: Partial<CMSNews>): Promise<CMSNews> {
    const response = await fetchAPI<{
      success: boolean;
      news: CMSNews;
    }>(`/news/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
    return response.news
  },

  async delete(id: string): Promise<void> {
    await fetchAPI(`/news/${id}`, {
      method: 'DELETE',
    })
  },
}

// Media Gallery API
export const mediaGalleryAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSMediaItem>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{ mediaItems: CMSMediaItem[]; totalItems: number; page: number; limit: number; totalPages: number; hasNextPage: boolean; hasPrevPage: boolean }>(`/media-gallery${queryString}`)
    
    return {
      data: response.mediaItems,
      totalDocs: response.totalItems,
      page: response.page,
      limit: response.limit,
      totalPages: response.totalPages,
      hasNextPage: response.hasNextPage,
      hasPrevPage: response.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSMediaItem>> {
    const response = await fetchAPI<{ mediaItem: CMSMediaItem }>(`/media-gallery/${id}`)
    return { data: response.mediaItem }
  },

  async getFeatured(limit = 12): Promise<CMSListResponse<CMSMediaItem>> {
    return this.getAll({ featured: true, limit })
  },

  async getByType(type: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSMediaItem>> {
    return this.getAll({ ...params, type })
  },

  async getByCategory(category: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSMediaItem>> {
    return this.getAll({ ...params, category })
  },
}

// Investment Opportunities API
export const investmentOpportunitiesAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSInvestmentOpportunity>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{ opportunities: CMSInvestmentOpportunity[]; totalOpportunities: number; page: number; limit: number; totalPages: number; hasNextPage: boolean; hasPrevPage: boolean }>(`/investment-opportunities${queryString}`)
    
    return {
      data: response.opportunities,
      totalDocs: response.totalOpportunities,
      page: response.page,
      limit: response.limit,
      totalPages: response.totalPages,
      hasNextPage: response.hasNextPage,
      hasPrevPage: response.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSInvestmentOpportunity>> {
    const response = await fetchAPI<{ opportunity: CMSInvestmentOpportunity }>(`/investment-opportunities/${id}`)
    return { data: response.opportunity }
  },

  async getFeatured(limit = 6): Promise<CMSListResponse<CMSInvestmentOpportunity>> {
    return this.getAll({ featured: true, limit })
  },

  async getUrgent(limit = 3): Promise<CMSListResponse<CMSInvestmentOpportunity>> {
    return this.getAll({ urgent: true, limit })
  },

  async getBySector(sector: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSInvestmentOpportunity>> {
    return this.getAll({ ...params, sector })
  },

  async getByInvestmentType(investmentType: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSInvestmentOpportunity>> {
    return this.getAll({ ...params, investmentType })
  },

  async create(data: Partial<CMSInvestmentOpportunity>): Promise<CMSInvestmentOpportunity> {
    const response = await fetchAPI<{
      success: boolean;
      investmentOpportunity: CMSInvestmentOpportunity;
    }>('/investment-opportunities', {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.investmentOpportunity
  },

  async update(id: string, data: Partial<CMSInvestmentOpportunity>): Promise<CMSInvestmentOpportunity> {
    const response = await fetchAPI<{
      success: boolean;
      investmentOpportunity: CMSInvestmentOpportunity;
    }>(`/investment-opportunities/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
    return response.investmentOpportunity
  },

  async delete(id: string): Promise<void> {
    await fetchAPI(`/investment-opportunities/${id}`, {
      method: 'DELETE',
    })
  },
}

// Contact Submissions API (for public contact form)
export const contactAPI = {
  async submit(data: {
    name: string
    email: string
    phone?: string
    organization?: string
    role?: string
    subject: string
    category: string
    message: string
    location?: {
      county?: string
      city?: string
      country?: string
    }
    priority?: string
  }): Promise<{ success: boolean; message: string; submissionId: string; status: string }> {
    return await fetchAPI<{ success: boolean; message: string; submissionId: string; status: string }>('/contact-submissions', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },
}

// Partners API
export const partnersAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSPartner>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{
      success: boolean;
      partners: CMSPartner[];
      pagination: {
        totalDocs: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
      }
    }>(`/partners${queryString}`)

    return {
      data: response.partners,
      totalDocs: response.pagination.totalDocs,
      page: response.pagination.page,
      limit: response.pagination.limit,
      totalPages: response.pagination.totalPages,
      hasNextPage: response.pagination.hasNextPage,
      hasPrevPage: response.pagination.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSPartner> {
    const response = await fetchAPI<{
      success: boolean;
      partner: CMSPartner;
    }>(`/partners/${id}`)
    return response.partner
  },

  async getFeatured(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSPartner>> {
    return this.getAll({ ...params, featured: true })
  },

  async getByType(type: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSPartner>> {
    return this.getAll({ ...params, type })
  },

  async create(data: Partial<CMSPartner>): Promise<CMSPartner> {
    const response = await fetchAPI<{
      success: boolean;
      partner: CMSPartner;
    }>('/partners', {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.partner
  },

  async update(id: string, data: Partial<CMSPartner>): Promise<CMSPartner> {
    const response = await fetchAPI<{
      success: boolean;
      partner: CMSPartner;
    }>(`/partners/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
    return response.partner
  },

  async delete(id: string): Promise<void> {
    await fetchAPI(`/partners/${id}`, {
      method: 'DELETE',
    })
  },
}

// Events API
export const eventsAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSEvent>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{
      success: boolean;
      events: CMSEvent[];
      pagination: {
        totalDocs: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
      }
    }>(`/events${queryString}`)

    return {
      data: response.events,
      totalDocs: response.pagination.totalDocs,
      page: response.pagination.page,
      limit: response.pagination.limit,
      totalPages: response.pagination.totalPages,
      hasNextPage: response.pagination.hasNextPage,
      hasPrevPage: response.pagination.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSEvent> {
    const response = await fetchAPI<{
      success: boolean;
      event: CMSEvent;
    }>(`/events/${id}`)
    return response.event
  },

  async getFeatured(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSEvent>> {
    return this.getAll({ ...params, featured: true })
  },

  async getUpcoming(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSEvent>> {
    return this.getAll({ ...params, upcoming: true })
  },

  async getByType(type: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSEvent>> {
    return this.getAll({ ...params, type })
  },

  async create(data: Partial<CMSEvent>): Promise<CMSEvent> {
    const response = await fetchAPI<{
      success: boolean;
      event: CMSEvent;
    }>('/events', {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.event
  },

  async update(id: string, data: Partial<CMSEvent>): Promise<CMSEvent> {
    const response = await fetchAPI<{
      success: boolean;
      event: CMSEvent;
    }>(`/events/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
    return response.event
  },

  async delete(id: string): Promise<void> {
    await fetchAPI(`/events/${id}`, {
      method: 'DELETE',
    })
  },
}

// Partnerships API
export const partnershipsAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSPartnership>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{
      success: boolean;
      partnerships: CMSPartnership[];
      pagination: {
        totalDocs: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
      }
    }>(`/partnerships${queryString}`)

    return {
      data: response.partnerships,
      totalDocs: response.pagination.totalDocs,
      page: response.pagination.page,
      limit: response.pagination.limit,
      totalPages: response.pagination.totalPages,
      hasNextPage: response.pagination.hasNextPage,
      hasPrevPage: response.pagination.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSPartnership> {
    const response = await fetchAPI<{
      success: boolean;
      partnership: CMSPartnership;
    }>(`/partnerships/${id}`)
    return response.partnership
  },

  async getFeatured(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSPartnership>> {
    return this.getAll({ ...params, featured: true })
  },

  async getByType(type: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSPartnership>> {
    return this.getAll({ ...params, type })
  },

  async getByStatus(status: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSPartnership>> {
    return this.getAll({ ...params, status })
  },

  async create(data: Partial<CMSPartnership>): Promise<CMSPartnership> {
    const response = await fetchAPI<{
      success: boolean;
      partnership: CMSPartnership;
    }>('/partnerships', {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.partnership
  },

  async update(id: string, data: Partial<CMSPartnership>): Promise<CMSPartnership> {
    const response = await fetchAPI<{
      success: boolean;
      partnership: CMSPartnership;
    }>(`/partnerships/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
    return response.partnership
  },

  async delete(id: string): Promise<void> {
    await fetchAPI(`/partnerships/${id}`, {
      method: 'DELETE',
    })
  },
}

// Export all APIs
export const cmsAPI = {
  projects: projectsAPI,
  successStories: successStoriesAPI,
  resources: resourcesAPI,
  news: newsAPI,
  mediaGallery: mediaGalleryAPI,
  investmentOpportunities: investmentOpportunitiesAPI,
  partnerships: partnershipsAPI,
  partners: partnersAPI,
  events: eventsAPI,
  contact: contactAPI,
}

export { CMSApiError }
