import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { partnerships<PERSON><PERSON><PERSON> } from '@/endpoints/partnerships'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      query: Object.fromEntries(request.nextUrl.searchParams.entries()),
      headers: {
        get: (name: string) => request.headers.get(name),
      },
      user: null,
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the handler
    await partnershipsHandler(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('Partnerships API error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Create new partnership
    const result = await payload.create({
      collection: 'partnerships',
      data: body,
    })

    return NextResponse.json({
      success: true,
      partnership: result,
      message: 'Partnership created successfully',
    })
  } catch (error) {
    console.error('Partnerships POST API error:', error)

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          message: error.message,
          details: error.data || error.details || null,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
