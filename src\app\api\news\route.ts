import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const urgent = searchParams.get('urgent')
    const search = searchParams.get('search')

    // Build where clause
    const where: any = {
      status: { equals: 'published' }
    }

    if (category) where.category = { equals: category }
    if (featured === 'true') where.featured = { equals: true }
    if (urgent === 'true') where.urgent = { equals: true }

    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } }
      ]
    }

    // Fetch news from PayloadCMS
    const result = await payload.find({
      collection: 'news',
      where,
      page,
      limit,
      sort: urgent === 'true' ? ['-urgent', '-publishDate'] :
            featured === 'true' ? ['-featured', '-publishDate'] : ['-publishDate'],
      populate: {
        'featuredImage.image': true,
        'location.county': true,
      },
    })

    return NextResponse.json({
      success: true,
      news: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        totalPages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    })
  } catch (error) {
    console.error('News API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Create new news item
    const result = await payload.create({
      collection: 'news',
      data: body,
    })

    return NextResponse.json({
      success: true,
      news: result,
      message: 'News item created successfully',
    })
  } catch (error) {
    console.error('News POST API error:', error)

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          message: error.message,
          details: error.data || error.details || null,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
