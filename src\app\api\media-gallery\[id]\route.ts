import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

interface RouteParams {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    let result

    // First try to find by ID (if it's a valid ObjectId format)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      try {
        result = await payload.findByID({
          collection: 'media-gallery',
          id,
        })
      } catch (error) {
        // If ID lookup fails, try slug lookup
        result = null
      }
    }

    // If no result from ID lookup or ID is not ObjectId format, try slug lookup
    if (!result) {
      const slugResult = await payload.find({
        collection: 'media-gallery',
        where: {
          slug: {
            equals: id,
          },
        },
        limit: 1,
      })

      if (slugResult.docs.length > 0) {
        result = slugResult.docs[0]
      }
    }

    if (!result) {
      return NextResponse.json(
        {
          success: false,
          error: 'Media item not found',
          message: `Media item with ID or slug '${id}' not found`,
        },
        { status: 404 },
      )
    }

    return NextResponse.json({
      success: true,
      mediaItem: result,
    })
  } catch (error) {
    console.error('Media Gallery GET API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    // First find the media item to get its actual ID
    let mediaId = id

    // If not a valid ObjectId, try to find by slug
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      const slugResult = await payload.find({
        collection: 'media-gallery',
        where: {
          slug: {
            equals: id,
          },
        },
        limit: 1,
      })

      if (slugResult.docs.length === 0) {
        return NextResponse.json(
          {
            error: 'Media item not found',
            message: `Media item with slug '${id}' not found`,
          },
          { status: 404 },
        )
      }

      mediaId = slugResult.docs[0].id
    }

    const body = await request.json()

    // Update the media item
    const result = await payload.update({
      collection: 'media-gallery',
      id: mediaId,
      data: body,
    })

    return NextResponse.json({
      success: true,
      mediaItem: result,
      message: 'Media item updated successfully',
    })
  } catch (error) {
    console.error('Media Gallery PUT API error:', error)

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          message: error.message,
          details: error.data || error.details || null,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    // First find the media item to get its actual ID
    let mediaId = id

    // If not a valid ObjectId, try to find by slug
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      const slugResult = await payload.find({
        collection: 'media-gallery',
        where: {
          slug: {
            equals: id,
          },
        },
        limit: 1,
      })

      if (slugResult.docs.length === 0) {
        return NextResponse.json(
          {
            error: 'Media item not found',
            message: `Media item with slug '${id}' not found`,
          },
          { status: 404 },
        )
      }

      mediaId = slugResult.docs[0].id
    }

    // Delete the media item
    await payload.delete({
      collection: 'media-gallery',
      id: mediaId,
    })

    return NextResponse.json({
      success: true,
      message: 'Media item deleted successfully',
    })
  } catch (error) {
    console.error('Media Gallery DELETE API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}
