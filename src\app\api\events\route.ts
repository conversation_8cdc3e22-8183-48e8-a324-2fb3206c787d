import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type')
    const upcoming = searchParams.get('upcoming')
    const featured = searchParams.get('featured')
    const search = searchParams.get('search')

    // Build where clause
    const where: any = {
      published: { equals: true }
    }

    if (type) where.type = { equals: type }
    if (featured === 'true') where.featured = { equals: true }

    // Add upcoming filter
    if (upcoming === 'true') {
      const now = new Date()
      where['schedule.startDate'] = { greater_than: now.toISOString() }
    }

    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } }
      ]
    }

    // Fetch events from PayloadCMS
    const result = await payload.find({
      collection: 'events',
      where,
      page,
      limit,
      sort: upcoming === 'true' ? ['schedule.startDate'] : ['-schedule.startDate'],
      populate: {
        'featuredImage.image': true,
        'location.county': true,
      },
    })

    return NextResponse.json({
      success: true,
      events: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        totalPages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    })
  } catch (error) {
    console.error('Events API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Create new event
    const result = await payload.create({
      collection: 'events',
      data: body,
    })

    return NextResponse.json({
      success: true,
      event: result,
      message: 'Event created successfully',
    })
  } catch (error) {
    console.error('Events POST API error:', error)

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          message: error.message,
          details: error.data || error.details || null,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
